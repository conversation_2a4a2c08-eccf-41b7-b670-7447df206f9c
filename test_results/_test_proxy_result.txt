Method: GET
Endpoint: /test-proxy
Status Code: 200
Duration: 7.0s
Response Body:
{
  "proxy_details": [
    {
      "proxy": "http://sp0o8xf1er:4Qw0OuwKmQ5=<EMAIL>:10001",
      "response_time_ms": 3239,
      "status_code": 407,
      "success": false
    },
    {
      "proxy": "http://sp0o8xf1er:4Qw0OuwKmQ5=<EMAIL>:10002",
      "response_time_ms": 3060,
      "status_code": 407,
      "success": false
    }
  ],
  "success_rate": 0,
  "successful_proxies": 0,
  "timestamp": "2025-06-13T16:15:22Z",
  "total_proxies": 2
}
