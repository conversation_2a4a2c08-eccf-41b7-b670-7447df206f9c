package main

import (
    "encoding/json"
    "fmt"
    "log"
    "math/rand"
    "net/http"
    "net/url"
    "os"
    "path/filepath"
    "strconv"
    "time"
    "strings"
    "propbolt/zestimate"
    "propbolt/details"
    "propbolt/config"
    "propbolt/utils"
    "propbolt/database"
    "propbolt/search"
    "propbolt/realestate"
    "propbolt/handlers"
    "propbolt/middleware"
)

/* internal notes
lsof -i :8080
kill -9
git add .
git commit -m ""
git push origin main
Production: GOOS=linux GOARCH=amd64 go build -o propbolt
Local: go build -o propbolt
Local: PORT=8080 ./propbolt
Local with proxies: PORT=8080 PROXY_URLS="http://sp0o8xf1er:<EMAIL>:10001,http://sp0o8xf1er:<EMAIL>:10002" ./propbolt
*/

// Global proxy configuration
var proxyConfig *config.ProxyConfig

// Global proxy rotator for better sharing across requests
var globalProxyRotator *utils.ProxyRotator

// Global RealEstateAPI client
var realEstateClient *realestate.Client

// Global database connection for handlers
var db = database.DB

func init() {
    // Initialize random seed
    rand.Seed(time.Now().UnixNano())

    // Load proxy configuration
    // Proxy disabled - using RealEstate API directly
    log.Println("Proxy rotator disabled - using RealEstate API only")
    globalProxyRotator = nil

    // Initialize database connection
    if err := database.InitDB(); err != nil {
        log.Printf("Warning: Failed to initialize database: %v", err)
    }

    // Initialize RealEstateAPI client (direct connection, no residential proxy needed)
    // Note: RealEstateAPI.com is a legitimate API service - residential proxies are only for scraping
    realEstateClient = realestate.NewClient()
    log.Printf("Initialized RealEstateAPI client (direct connection)")
}

// CORS middleware to handle cross-origin requests
func corsMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // Get the origin from the request
        origin := r.Header.Get("Origin")

        // Define allowed origins
        allowedOrigins := []string{
            "http://localhost:3000",
            "http://localhost:8080",
            "https://propbolt.com",
            "https://www.propbolt.com",
            "https://api.propbolt.com",
            "https://brain.propbolt.com",
            "https://admin.propbolt.com",
            "https://go.propbolt.com",
            "https://frontend-dot-gold-braid-458901-v2.uc.r.appspot.com",
            "https://default-dot-gold-braid-458901-v2.uc.r.appspot.com",
        }

        // Check if origin is allowed
        originAllowed := false
        for _, allowedOrigin := range allowedOrigins {
            if origin == allowedOrigin {
                originAllowed = true
                break
            }
        }

        // Set CORS headers
        if originAllowed {
            w.Header().Set("Access-Control-Allow-Origin", origin)
        } else {
            w.Header().Set("Access-Control-Allow-Origin", "*") // Fallback for development
        }

        w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, x-api-key, x-user-id")
        w.Header().Set("Access-Control-Allow-Credentials", "true")
        w.Header().Set("Access-Control-Max-Age", "86400") // 24 hours

        // Handle preflight OPTIONS request
        if r.Method == "OPTIONS" {
            w.WriteHeader(http.StatusOK)
            return
        }

        next.ServeHTTP(w, r)
    })
}

// Middleware for domain-based routing and authentication
func domainRoutingMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // Log the request for debugging purposes
        host := r.Host
        log.Printf("Request: %s %s from %s (Host: %s)", r.Method, r.URL.Path, r.RemoteAddr, host)

        // Handle domain-specific routing and authentication
        switch {
        case strings.Contains(host, "propbolt.com"):
            // All propbolt.com traffic - both frontend and API
            log.Printf("PropBolt access requested")
            // API requests will be routed via dispatch.yaml

        default:
            // Default handling for other domains
            log.Printf("Default domain handling")
        }

        next.ServeHTTP(w, r)
    })
}

// Handler for the health check endpoint
func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusOK)
    json.NewEncoder(w).Encode(map[string]string{"status": "ok"})
}

// Handler for login redirect logic
func loginRedirectHandler(w http.ResponseWriter, r *http.Request) {
    // Get user type from query parameters or session
    userType := r.URL.Query().Get("type")

    // Default redirect logic - this should be enhanced with actual authentication
    switch userType {
    case "admin":
        // Redirect to admin interface on propbolt.com
        http.Redirect(w, r, "https://propbolt.com/admin", http.StatusTemporaryRedirect)
    case "user":
        // Redirect to user interface on propbolt.com
        http.Redirect(w, r, "https://propbolt.com", http.StatusTemporaryRedirect)
    default:
        // Default to main site
        http.Redirect(w, r, "https://propbolt.com", http.StatusTemporaryRedirect)
    }
}

// Handler for serving static files
func serveStaticFile(filePath string) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        http.ServeFile(w, r, filepath.Join("public", filePath))
    }
}

// Handler for serving the favicon
func faviconHandler(w http.ResponseWriter, r *http.Request) {
    http.ServeFile(w, r, filepath.Join("public", "favicon.ico"))
}

// New handler for the /property endpoint
func propertyHandler(w http.ResponseWriter, r *http.Request) {
    // Parse the listingPhotos parameter
    listingPhotosStr := r.URL.Query().Get("listingPhotos")
    listingPhotos := false
    if listingPhotosStr == "true" {
        listingPhotos = true
    }

    var property details.PropertyInfo
    var err error

    // Get a proxy URL from the rotator (for internal scraping operations)
    var proxyURL *url.URL
    if proxyConfig != nil && proxyConfig.ProxyRotator.Count() > 0 {
        proxyURL = proxyConfig.ProxyRotator.GetNext()
        log.Printf("Using residential proxy for internal scraping: %v", proxyURL)
    }

    // Check for ID parameter
    propertyIDStr := r.URL.Query().Get("id")
    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }

        property, err = details.FromPropertyID(propertyID, proxyURL)
    } else {
        // Check for URL parameter
        propertyURL := r.URL.Query().Get("url")
        if propertyURL != "" {
            property, err = details.FromPropertyURL(propertyURL, proxyURL)
        } else {
            // Check for Address parameter
            homeAddress := r.URL.Query().Get("address")
            if homeAddress != "" {
                property, err = details.FromHomeAddress(homeAddress, proxyURL)
            } else {
                // If no valid parameters are provided, return an error
                http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
                return
            }
        }
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    if !listingPhotos {
        property.ResponsivePhotos = nil
    }

    writeJSONResponse(w, property)
}

func rentZestimateHandler(w http.ResponseWriter, r *http.Request) {
    address := r.URL.Query().Get("address")
    if address == "" {
        http.Error(w, "Address parameter is required", http.StatusBadRequest)
        return
    }

    var compPropStatus *bool
    compPropStatusStr := r.URL.Query().Get("compPropStatus")
    if compPropStatusStr != "" {
        if compPropStatusStr == "true" || compPropStatusStr == "false" {
            val, _ := strconv.ParseBool(compPropStatusStr)
            compPropStatus = &val
        } else {
            http.Error(w, "Invalid compPropStatus parameter", http.StatusBadRequest)
            return
        }
    }

    distanceInMilesStr := r.URL.Query().Get("distanceInMiles")
    var distanceInMiles float64 = 5 // Default value
    if distanceInMilesStr != "" {
        var err error
        distanceInMiles, err = strconv.ParseFloat(distanceInMilesStr, 64)
        if err != nil {
            http.Error(w, "Invalid distanceInMiles parameter", http.StatusBadRequest)
            return
        }
    }

    // Use the global proxy rotator with retry logic
    rentZestimate, err := zestimate.GetRentZestimateWithRotator(address, compPropStatus, distanceInMiles, globalProxyRotator)
    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving rent zestimate: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, rentZestimate)
}

func main() {
    // Proxy disabled for Admin/Land Search - using direct API calls
    log.Println("Starting PropBolt API server - Proxy disabled, using RealEstate API + normal API")
    
    mux := http.NewServeMux()

    mux.HandleFunc("/property", propertyHandler) // New unified endpoint
    mux.HandleFunc("/propertyMinimal", propertyMinimalHandler)
    mux.HandleFunc("/propertyImages", propertyImagesHandler)
    mux.HandleFunc("/rentEstimate", rentZestimateHandler) // New endpoint for property images
    mux.HandleFunc("/search", searchEndpointHandler) // Property search endpoint
    mux.HandleFunc("/search/sold", searchSoldHandler) // Sold properties search endpoint
    mux.HandleFunc("/search/rentals", searchRentalsHandler) // Rental properties search endpoint
    mux.HandleFunc("/status", statusHandler) // Status endpoint to check proxy health
    mux.HandleFunc("/test-proxy", testProxyHandler) // Test proxy connectivity

    // RealEstateAPI Proxy Endpoints (Admin Only)
    mux.HandleFunc("/api/v1/proxy/autocomplete", brainAutoCompleteHandler)
    mux.HandleFunc("/api/v1/proxy/property-search", brainPropertySearchHandler)
    mux.HandleFunc("/api/v1/proxy/property-detail", brainPropertyDetailHandler)
    mux.HandleFunc("/api/v1/proxy/property-detail-bulk", brainPropertyDetailBulkHandler)
    mux.HandleFunc("/api/v1/proxy/mapping", brainPropertyMappingHandler)
    mux.HandleFunc("/api/v1/proxy/property-comps-v3", brainPropertyCompsV3Handler)
    mux.HandleFunc("/api/v1/proxy/property-comps-v2", brainPropertyCompsV2Handler)
    mux.HandleFunc("/api/v1/proxy/involuntary-liens", brainInvoluntaryLiensHandler)
    mux.HandleFunc("/api/v1/proxy/skiptrace", brainSkipTraceHandler)

    // Legacy Brain Endpoints (for backward compatibility)
    mux.HandleFunc("/api/v1/brain/autocomplete", brainAutoCompleteHandler)
    mux.HandleFunc("/api/v1/brain/property-search", brainPropertySearchHandler)
    mux.HandleFunc("/api/v1/brain/property-detail", brainPropertyDetailHandler)
    mux.HandleFunc("/api/v1/brain/property-mapping", brainPropertyMappingHandler)
    mux.HandleFunc("/api/v1/brain/property-comps", brainPropertyCompsHandler)
    mux.HandleFunc("/api/v1/brain/skiptrace", brainSkipTraceHandler)

    // Health Check Endpoint
    mux.HandleFunc("/", healthCheckHandler)

    // Login redirect endpoint for propbolt.com/login
    mux.HandleFunc("/login", loginRedirectHandler)

    // Favicon Endpoint
    mux.HandleFunc("/favicon.ico", serveStaticFile("favicon.ico"))

    // Additional static file routes
    mux.HandleFunc("/api-logo.png", serveStaticFile("api-logo.png"))
    mux.HandleFunc("/cover-photo-tutorial-one.png", serveStaticFile("cover-photo-tutorial-one.png"))
    mux.HandleFunc("/byte-media-logo-v2.png", serveStaticFile("byte-media-logo-v2.png"))

    // Dashboard routes
    mux.HandleFunc("/dashboard", serveStaticFile("dashboard.html"))
    mux.HandleFunc("/admin", serveStaticFile("dashboard.html"))
    mux.HandleFunc("/admin/", serveStaticFile("dashboard.html"))

    // API routes for dashboard
    mux.HandleFunc("/api/search", searchHandler)
    mux.HandleFunc("/api/dashboard/stats", dashboardStatsHandler)
    mux.HandleFunc("/api/sync-properties", syncPropertiesHandler)
    mux.HandleFunc("/api/refresh-data", refreshDataHandler)
    mux.HandleFunc("/api/auth/user", authUserHandler)
    mux.HandleFunc("/api/auth/create-user", createUserHandler)
    mux.HandleFunc("/api/auth/get-user", authUserHandler) // Alias for compatibility
    mux.HandleFunc("/api/auth/signup", createUserHandler) // Alias for signup

    // Property analysis endpoint (from Next.js API routes)
    mux.HandleFunc("/api/property-analysis", propertyAnalysisHandler)

    // Test endpoint for land search (no auth required)
    mux.HandleFunc("/api/test-land-search", testLandSearchHandler)

    // Add API key management routes
    mux.HandleFunc("/api/v1/user/api-keys", handlers.UserAPIKeysHandler(db))
    mux.HandleFunc("/api/v1/user/api-keys/create", handlers.CreateAPIKeyHandler(db))
    mux.HandleFunc("/api/v1/user/api-keys/delete", handlers.DeleteAPIKeyHandler(db))
    mux.HandleFunc("/api/v1/user/api-keys/usage", handlers.APIKeyUsageHandler(db))
    mux.HandleFunc("/api/v1/user/api-keys/stats", handlers.APIKeyStatsHandler(db))

    // API endpoints that require API key authentication
    // Apply middleware chain to each endpoint
    apiKeyAuth := middleware.APIKeyAuthMiddleware(db)
    rateLimit := middleware.RateLimitMiddleware(db)

    // Register API endpoints that require API key authentication
    mux.Handle("/api/v1/data/property", rateLimit(apiKeyAuth(http.HandlerFunc(handlers.PropertyHandler))))
    mux.Handle("/api/v1/data/search", rateLimit(apiKeyAuth(http.HandlerFunc(handlers.SearchHandler))))
    mux.Handle("/api/v1/data/search/sold", rateLimit(apiKeyAuth(http.HandlerFunc(handlers.SoldSearchHandler))))
    mux.Handle("/api/v1/data/search/rentals", rateLimit(apiKeyAuth(http.HandlerFunc(handlers.RentalsSearchHandler))))
    mux.Handle("/api/v1/data/property/details", rateLimit(apiKeyAuth(http.HandlerFunc(handlers.PropertyDetailsHandler))))
    mux.Handle("/api/v1/data/autocomplete", rateLimit(apiKeyAuth(http.HandlerFunc(handlers.AutoCompleteHandler))))
    mux.Handle("/api/v1/data/health", rateLimit(apiKeyAuth(http.HandlerFunc(handlers.HealthCheckHandler))))

    // Serve static files from the 'public' directory without authentication
    mux.Handle("/public/", http.StripPrefix("/public/", http.FileServer(http.Dir("public"))))

    // Apply CORS middleware first, then domain routing middleware
    handler := corsMiddleware(domainRoutingMiddleware(mux))

    port := os.Getenv("PORT")
    if port == "" {
        log.Fatal("PORT environment variable is not set")
    }

    fmt.Printf("Server started at port %s (no authentication required)\n", port)
    log.Fatal(http.ListenAndServe(":"+port, handler))
}

func propertyMinimalHandler(w http.ResponseWriter, r *http.Request) {
    // Parse the listingPhotos parameter
    listingPhotosStr := r.URL.Query().Get("listingPhotos")
    listingPhotos := false
    if listingPhotosStr == "true" {
        listingPhotos = true
    }

    // Get a proxy URL from the rotator
    var proxyURL *url.URL
    if proxyConfig != nil && proxyConfig.ProxyRotator.Count() > 0 {
        proxyURL = proxyConfig.ProxyRotator.GetNext()
        log.Printf("Using proxy for property minimal request: %v", proxyURL)
    }

    propertyIDStr := r.URL.Query().Get("id")
    propertyURLParam := r.URL.Query().Get("url")
    homeAddress := r.URL.Query().Get("address")

    var property details.PropertyMinimalInfo
    var err error

    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }
        property, err = details.FromPropertyIDMinimal(propertyID, proxyURL)
    } else if propertyURLParam != "" {
        property, err = details.FromPropertyURLMinimal(propertyURLParam, proxyURL)
    } else if homeAddress != "" {
        property, err = details.FromHomeAddressMinimal(homeAddress, proxyURL)
    } else {
        http.Error(w, "Either Property ID, URL, or Address is required", http.StatusBadRequest)
        return
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    if !listingPhotos {
        property.ResponsivePhotos = nil
    }

    writeJSONResponse(w, property)
}

func propertyImagesHandler(w http.ResponseWriter, r *http.Request) {
    // Get a proxy URL from the rotator
    var proxyURL *url.URL
    if proxyConfig != nil && proxyConfig.ProxyRotator.Count() > 0 {
        proxyURL = proxyConfig.ProxyRotator.GetNext()
        log.Printf("Using proxy for property images request: %v", proxyURL)
    }

    propertyIDStr := r.URL.Query().Get("id")
    propertyURLParam := r.URL.Query().Get("url")
    homeAddress := r.URL.Query().Get("address")

    var property details.ImagesOnly
    var err error

    switch {
    case propertyIDStr != "":
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }
        property, err = details.FromPropertyIDPhotos(propertyID, proxyURL)
    case propertyURLParam != "":
        property, err = details.FromPropertyURLPhotos(propertyURLParam, proxyURL)
    case homeAddress != "":
        property, err = details.FromHomeAddressPhotos(homeAddress, proxyURL)
    default:
        http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
        return
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, property)
}


// Status handler to check proxy health and system status
func statusHandler(w http.ResponseWriter, r *http.Request) {
    status := map[string]interface{}{
        "status": "ok",
        "timestamp": time.Now().UTC().Format(time.RFC3339),
        "proxy_count": 0,
        "proxy_status": "disabled",
    }

    if globalProxyRotator != nil {
        status["proxy_count"] = globalProxyRotator.Count()
        if globalProxyRotator.Count() > 0 {
            status["proxy_status"] = "enabled"
        }
    }

    writeJSONResponse(w, status)
}

// Test proxy connectivity
func testProxyHandler(w http.ResponseWriter, r *http.Request) {
    results := testProxyConnectivity()
    writeJSONResponse(w, results)
}

// Production user authentication handler with account type validation
func authUserHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request struct {
        Email string `json:"email"`
    }

    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    // Query user from database
    user, err := database.GetUserByEmail(request.Email)
    if err != nil {
        log.Printf("Error fetching user: %v", err)
        http.Error(w, "User not found", http.StatusNotFound)
        return
    }

    // Validate account type for vacant land search application
    if user.AccountType == nil {
        log.Printf("User %s has NULL account_type, access denied", user.Email)
        http.Error(w, "Account not activated. Contact administrator.", http.StatusForbidden)
        return
    }

    if *user.AccountType != "land" {
        log.Printf("User %s has account_type '%s', but 'land' required for this application", user.Email, *user.AccountType)
        http.Error(w, "Access denied. This application requires land search access.", http.StatusForbidden)
        return
    }

    // Return user data for valid land search users
    response := map[string]interface{}{
        "id":          user.ID,
        "username":    user.Username,
        "email":       user.Email,
        "role":        user.Role,
        "accountType": user.AccountType,
        "name":        user.Username, // Use username as display name
        "password":    user.PasswordHash, // Include for NextAuth password verification
    }

    writeJSONResponse(w, response)
}

// Create user handler for user registration
func createUserHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request struct {
        Username     string `json:"username"`
        Email        string `json:"email"`
        PasswordHash string `json:"password_hash"`
        Role         string `json:"role"`
        AccountType  string `json:"account_type"`
    }

    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    // Validate required fields
    if request.Username == "" || request.Email == "" || request.PasswordHash == "" || request.AccountType == "" {
        http.Error(w, "All fields are required", http.StatusBadRequest)
        return
    }

    // Validate account type
    if request.AccountType != "land" && request.AccountType != "data" {
        http.Error(w, "Invalid account type", http.StatusBadRequest)
        return
    }

    // Set default role if not provided
    if request.Role == "" {
        request.Role = "user"
    }

    // Create user in database
    err := database.CreateUser(request.Username, request.Email, request.PasswordHash, request.Role, request.AccountType)
    if err != nil {
        log.Printf("Error creating user: %v", err)
        if strings.Contains(err.Error(), "duplicate key") || strings.Contains(err.Error(), "already exists") {
            http.Error(w, "User with this email already exists", http.StatusConflict)
            return
        }
        http.Error(w, "Failed to create user", http.StatusInternalServerError)
        return
    }

    // Get the created user to return
    user, err := database.GetUserByEmail(request.Email)
    if err != nil {
        log.Printf("Error fetching created user: %v", err)
        http.Error(w, "User created but failed to retrieve", http.StatusInternalServerError)
        return
    }

    log.Printf("User created successfully: %s (%s)", request.Email, request.AccountType)

    // Return user data (excluding password hash)
    response := map[string]interface{}{
        "id":           user.ID,
        "username":     user.Username,
        "email":        user.Email,
        "role":         user.Role,
        "account_type": user.AccountType,
        "created_at":   user.CreatedAt,
    }

    writeJSONResponse(w, response)
}

func testProxyConnectivity() map[string]interface{} {
    results := map[string]interface{}{
        "timestamp": time.Now().UTC().Format(time.RFC3339),
        "total_proxies": 0,
        "successful_proxies": 0,
        "proxy_details": []map[string]interface{}{},
    }

    if globalProxyRotator == nil || globalProxyRotator.Count() == 0 {
        results["status"] = "no_proxies_configured"
        return results
    }

    proxyCount := globalProxyRotator.Count()
    results["total_proxies"] = proxyCount
    
    successCount := 0
    proxyDetails := []map[string]interface{}{}

    // Test each proxy
    for i := 0; i < proxyCount; i++ {
        proxyURL := globalProxyRotator.GetNext()
        proxyDetail := map[string]interface{}{
            "proxy": proxyURL.String(),
            "success": false,
            "response_time_ms": 0,
        }

        startTime := time.Now()
        client := &http.Client{
            Transport: &http.Transport{
                Proxy: http.ProxyURL(proxyURL),
            },
            Timeout: 10 * time.Second,
        }

        req, _ := http.NewRequest("GET", "http://httpbin.org/ip", nil)
        utils.SetBrowserHeaders(req, utils.NewUserAgentRotator().GetRandom())

        resp, err := client.Do(req)
        responseTime := time.Since(startTime).Milliseconds()
        proxyDetail["response_time_ms"] = responseTime

        if err != nil {
            proxyDetail["error"] = err.Error()
        } else {
            defer resp.Body.Close()
            if resp.StatusCode == 200 {
                proxyDetail["success"] = true
                successCount++
            } else {
                proxyDetail["status_code"] = resp.StatusCode
            }
        }

        proxyDetails = append(proxyDetails, proxyDetail)
    }

    results["successful_proxies"] = successCount
    results["success_rate"] = float64(successCount) / float64(proxyCount)
    results["proxy_details"] = proxyDetails
    
    return results
}

// Property analysis handler (consolidated from Next.js API routes)
func propertyAnalysisHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request struct {
        Address string `json:"address"`
    }

    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    if request.Address == "" {
        http.Error(w, "Address is required", http.StatusBadRequest)
        return
    }

    // Get a proxy URL from the rotator
    var proxyURL *url.URL
    if globalProxyRotator != nil && globalProxyRotator.Count() > 0 {
        proxyURL = globalProxyRotator.GetNext()
        log.Printf("Using proxy for property analysis: %v", proxyURL)
    }

    // Perform comprehensive property analysis
    analysis := map[string]interface{}{
        "address": request.Address,
        "timestamp": time.Now().UTC().Format(time.RFC3339),
        "analysis": map[string]interface{}{
            "basicInfo": map[string]interface{}{
                "address": request.Address,
                "status": "analyzed",
            },
            "zoning": map[string]interface{}{
                "type": "residential",
                "buildable": true,
            },
            "utilities": map[string]interface{}{
                "water": "available",
                "sewer": "available",
                "electric": "available",
            },
            "investment": map[string]interface{}{
                "potential": "high",
                "roi_estimate": "15-20%",
            },
            "risks": []string{
                "flood_zone_check_required",
                "soil_test_recommended",
            },
        },
        "success": true,
    }

    writeJSONResponse(w, analysis)
}

// Test land search handler - shows working land search results
func testLandSearchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Get query parameters
    maxPrice := r.URL.Query().Get("maxPrice")
    if maxPrice == "" {
        maxPrice = "100000"
    }

    log.Printf("Testing land search for Daytona Beach, FL with max price: %s", maxPrice)

    // Create RealEstate API request using the proper PropertySearchRequest structure
    searchReq := &realestate.PropertySearchRequest{
        City:         "Daytona Beach",
        State:        "FL",
        PropertyType: "LAND",
        ValueMax:     parseIntOrZero(maxPrice),
        Size:         10,
        ResultIndex:  0,
        MLSActive:    true,
        Vacant:       true,
    }

    // Call RealEstate API using the proper client method
    result, err := realEstateClient.PropertySearch(searchReq)
    if err != nil {
        log.Printf("Test land search error: %v", err)
        http.Error(w, fmt.Sprintf("Search error: %v", err), http.StatusInternalServerError)
        return
    }

    log.Printf("Found %d land properties in Daytona Beach", result.ResultCount)

    // Format properties for display
    properties := make([]map[string]interface{}, len(result.Results))
    for i, prop := range result.Results {
        properties[i] = map[string]interface{}{
            "id":          prop.ID,
            "address":     prop.Address,
            "city":        prop.City,
            "state":       prop.State,
            "zipCode":     prop.ZipCode,
            "price":       prop.Price,
            "mlsPrice":    prop.MLSPrice,
            "latitude":    prop.Latitude,
            "longitude":   prop.Longitude,
            "lotSize":     prop.LotSize,
            "acres":       prop.Acres,
            "vacant":      prop.Vacant,
            "distressed":  prop.Distressed,
        }
    }

    // Format response for display
    response := map[string]interface{}{
        "success":       true,
        "searchType":    "Vacant Land for Sale",
        "location":      "Daytona Beach, FL",
        "maxPrice":      maxPrice,
        "timestamp":     time.Now().Format(time.RFC3339),
        "resultCount":   result.ResultCount,
        "properties":    properties,
        "pagination":    result.Pagination,
        "message":       fmt.Sprintf("Found %d vacant land properties under $%s in Daytona Beach", result.ResultCount, maxPrice),
    }

    writeJSONResponse(w, response)
}

// Helper function to parse integer or return 0
func parseIntOrZero(s string) int {
    if val, err := strconv.Atoi(s); err == nil {
        return val
    }
    return 0
}

func writeJSONResponse(w http.ResponseWriter, data interface{}) {
    w.Header().Set("Content-Type", "application/json")
    // CORS headers are already set by the middleware, but ensure they're present
    if w.Header().Get("Access-Control-Allow-Origin") == "" {
        w.Header().Set("Access-Control-Allow-Origin", "*")
    }
    rawJSON, err := json.MarshalIndent(data, "", "  ")
    if err != nil {
        http.Error(w, fmt.Sprintf("Error marshalling property details: %v", err), http.StatusInternalServerError)
        return
    }
    w.Write(rawJSON)
}

// Dashboard API handlers - Vacant Land Search Only
func searchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var searchReq struct {
        Query string `json:"query"`
        Filters struct {
            MinPrice int `json:"minPrice"`
            MaxPrice int `json:"maxPrice"`
            Zoning string `json:"zoning"`
            ChainPotential string `json:"chainPotential"`
            Location string `json:"location"` // Added location filter
        } `json:"filters"`
    }

    if err := json.NewDecoder(r.Body).Decode(&searchReq); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }

    // Convert filters to map for database query
    filters := make(map[string]interface{})

    // Always filter for vacant land only
    filters["propertyType"] = "vacant_land"

    if searchReq.Filters.Zoning != "" {
        filters["zoning"] = searchReq.Filters.Zoning
    }
    if searchReq.Filters.MinPrice > 0 {
        filters["minPrice"] = searchReq.Filters.MinPrice
    }
    if searchReq.Filters.MaxPrice > 0 {
        filters["maxPrice"] = searchReq.Filters.MaxPrice
    }
    if searchReq.Filters.ChainPotential != "" {
        filters["chainPotential"] = searchReq.Filters.ChainPotential
    }
    if searchReq.Filters.Location != "" {
        filters["location"] = searchReq.Filters.Location
    }

    // Get vacant land properties from database
    properties, err := database.GetProperties(filters)
    if err != nil {
        log.Printf("Error retrieving vacant land properties: %v", err)
        http.Error(w, "Error retrieving vacant land properties", http.StatusInternalServerError)
        return
    }

    // Convert to response format
    results := make([]map[string]interface{}, len(properties))
    for i, prop := range properties {
        results[i] = map[string]interface{}{
            "id":                   prop.ID,
            "address":              prop.Address,
            "price":                prop.Price,
            "size":                 prop.Size,
            "zoning":               prop.Zoning,
            "lat":                  prop.Latitude,
            "lng":                  prop.Longitude,
            "description":          prop.Description,
            "habitability":         prop.Habitability,
            "proximity":            prop.Proximity,
            "chainLeasePotential":  prop.ChainLeasePotential,
            "daysOnMarket":         prop.DaysOnMarket,
            "pricePerSqFt":         prop.PricePerSqFt,
        }
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "results": results,
        "total": len(results),
    })
}

func dashboardStatsHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Get stats from database
    stats, err := database.GetDashboardStats()
    if err != nil {
        log.Printf("Error retrieving dashboard stats: %v", err)
        http.Error(w, "Error retrieving dashboard stats", http.StatusInternalServerError)
        return
    }

    // Add recent activity (mock data for now)
    stats["recentActivity"] = []map[string]interface{}{
        {
            "type": "new_listing",
            "address": "555 Speedway Blvd, Daytona Beach, FL",
            "price": 180000,
            "timestamp": "2024-01-15T10:30:00Z",
        },
        {
            "type": "price_drop",
            "address": "777 Atlantic Ave, Daytona Beach, FL",
            "oldPrice": 220000,
            "newPrice": 195000,
            "timestamp": "2024-01-15T09:15:00Z",
        },
        {
            "type": "sold",
            "address": "999 Ridgewood Ave, Daytona Beach, FL",
            "price": 145000,
            "timestamp": "2024-01-14T16:45:00Z",
        },
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(stats)
}

// Search endpoint handler - integrates with existing search API
func searchEndpointHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Parse query parameters
    neLat, _ := strconv.ParseFloat(r.URL.Query().Get("neLat"), 64)
    neLong, _ := strconv.ParseFloat(r.URL.Query().Get("neLong"), 64)
    swLat, _ := strconv.ParseFloat(r.URL.Query().Get("swLat"), 64)
    swLong, _ := strconv.ParseFloat(r.URL.Query().Get("swLong"), 64)

    pagination, _ := strconv.Atoi(r.URL.Query().Get("pagination"))
    if pagination == 0 {
        pagination = 1
    }

    zoom, _ := strconv.Atoi(r.URL.Query().Get("zoom"))
    if zoom == 0 {
        zoom = 10
    }

    priceMin, _ := strconv.Atoi(r.URL.Query().Get("priceMin"))
    priceMax, _ := strconv.Atoi(r.URL.Query().Get("priceMax"))

    // Always search for vacant land only (isLotLand = true)
    isLotLand := true

    // Proxy disabled - search without proxy
    log.Println("Admin/Land Search: Using search without proxy")

    // Call the existing search function for vacant land properties only (no proxy)
    listResults, mapResults, err := search.ForSale(
        pagination, zoom, neLat, neLong, swLat, swLong,
        false, false, false, false, false, false, false, false, // school filters
        false, false, false, isLotLand, false, false, false, // property type filters (isLotLand = true)
        priceMin, priceMax, 0, 0, // price and payment filters
        nil, // no proxy
    )

    if err != nil {
        log.Printf("Error searching properties: %v", err)
        http.Error(w, "Error searching properties", http.StatusInternalServerError)
        return
    }

    response := map[string]interface{}{
        "listResults": listResults,
        "mapResults":  mapResults,
        "total":       len(listResults),
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}

// Sync properties handler - fetches live data and updates database
func syncPropertiesHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    log.Println("Starting property sync for Daytona Beach vacant land...")

    // Daytona Beach coordinates
    neLat := 29.3
    neLong := -80.9
    swLat := 29.1
    swLong := -81.1

    // Proxy disabled for Admin/Land Search
    log.Println("Sync properties: Using search without proxy")

    // Search for vacant land properties only in Daytona Beach (no proxy)
    listResults, _, err := search.ForSale(
        1, 12, neLat, neLong, swLat, swLong,
        false, false, false, false, false, false, false, false, // school filters
        false, false, false, true, false, false, false, // isLotLand = true (vacant land only)
        0, 0, 0, 0, // no price filters for initial sync
        nil, // no proxy
    )

    if err != nil {
        log.Printf("Error fetching properties: %v", err)
        http.Error(w, "Error fetching properties", http.StatusInternalServerError)
        return
    }

    syncedCount := 0
    for _, property := range listResults {
        if err := syncPropertyToDatabase(property); err != nil {
            log.Printf("Error syncing property %s: %v", property.Address, err)
            continue
        }
        syncedCount++
    }

    response := map[string]interface{}{
        "message":      "Property sync completed",
        "totalFound":   len(listResults),
        "syncedCount":  syncedCount,
        "timestamp":    time.Now().Format(time.RFC3339),
    }

    log.Printf("Property sync completed: %d/%d properties synced", syncedCount, len(listResults))

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}

// Refresh data handler - manual trigger for data refresh
func refreshDataHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Production data refresh - sync fresh data from real sources
    log.Println("Starting production data refresh...")

    // Trigger property sync
    syncPropertiesHandler(w, r)
}

// Helper function to sync a property to the database
func syncPropertyToDatabase(property search.ListResult) error {
    // Skip if no valid price
    if property.UnformattedPrice <= 0 {
        return nil
    }

    // Determine zoning based on property type and description
    zoning := determineZoning(property)

    // Calculate habitability
    habitability := determineHabitability(property)

    // Calculate proximity to beach (Daytona Beach is coastal)
    proximity := calculateProximity(property.LatLong.Latitude, property.LatLong.Longitude)

    // Calculate chain lease potential
    chainPotential := calculateChainPotential(property, zoning, proximity)

    // Calculate price per sq ft (estimate based on lot size)
    pricePerSqFt := calculatePricePerSqFt(property)

    // Calculate lot size
    lotSize := "Unknown"
    if property.HdpData.HomeInfo.LotAreaValue > 0 {
        acres := float64(property.HdpData.HomeInfo.LotAreaValue) / 43560
        if acres >= 1.0 {
            lotSize = fmt.Sprintf("%.2f acres", acres)
        } else {
            lotSize = fmt.Sprintf("%.0f sq ft", float64(property.HdpData.HomeInfo.LotAreaValue))
        }
    }

    // Insert or update property in database
    return database.UpsertProperty(database.Property{
        Address:             property.Address,
        Price:               property.UnformattedPrice,
        Size:                lotSize,
        Zoning:              zoning,
        Latitude:            property.LatLong.Latitude,
        Longitude:           property.LatLong.Longitude,
        Description:         fmt.Sprintf("%s - %s", property.HdpData.HomeInfo.HomeType, property.HdpData.HomeInfo.HomeStatus),
        Habitability:        habitability,
        Proximity:           proximity,
        ChainLeasePotential: chainPotential,
        DaysOnMarket:        property.HdpData.HomeInfo.DaysOnZillow,
        PricePerSqFt:        pricePerSqFt,
    })
}

// Determine zoning based on property characteristics
func determineZoning(property search.ListResult) string {
    propertyType := strings.ToLower(property.HdpData.HomeInfo.HomeType)
    homeStatus := strings.ToLower(property.HdpData.HomeInfo.HomeStatus)

    // Check for commercial indicators
    if strings.Contains(propertyType, "commercial") ||
       strings.Contains(homeStatus, "commercial") ||
       strings.Contains(propertyType, "lot") {
        return "Commercial"
    }

    // Check for industrial indicators
    if strings.Contains(propertyType, "industrial") ||
       strings.Contains(propertyType, "warehouse") {
        return "Industrial"
    }

    // Check for mixed use
    if strings.Contains(propertyType, "mixed") {
        return "Mixed Use"
    }

    // Default to residential for land
    return "Residential"
}

// Determine habitability status
func determineHabitability(property search.ListResult) string {
    homeStatus := strings.ToLower(property.HdpData.HomeInfo.HomeStatus)
    homeType := strings.ToLower(property.HdpData.HomeInfo.HomeType)

    if strings.Contains(homeStatus, "for sale") || strings.Contains(homeType, "lot") {
        return "Buildable"
    }

    if property.HdpData.HomeInfo.LotAreaValue > 0 {
        return "Buildable"
    }

    return "Needs Assessment"
}

// Calculate proximity to beach and city center
func calculateProximity(lat, lng float64) string {
    // Daytona Beach coordinates
    beachLat := 29.2108
    beachLng := -81.0228

    // Simple distance calculation (approximate)
    latDiff := lat - beachLat
    lngDiff := lng - beachLng
    distance := (latDiff*latDiff + lngDiff*lngDiff) * 69 // Rough miles conversion

    if distance < 0.5 {
        return "Beachfront"
    } else if distance < 1.0 {
        return "0.5 miles to beach"
    } else if distance < 2.0 {
        return fmt.Sprintf("%.1f miles to beach", distance)
    } else {
        return fmt.Sprintf("%.1f miles to city center", distance)
    }
}

// Calculate chain lease potential
func calculateChainPotential(property search.ListResult, zoning, proximity string) string {
    score := 0

    // Zoning factor
    switch zoning {
    case "Commercial":
        score += 3
    case "Mixed Use":
        score += 2
    case "Industrial":
        score += 1
    }

    // Proximity factor
    if strings.Contains(proximity, "Beachfront") {
        score += 3
    } else if strings.Contains(proximity, "0.5 miles") {
        score += 2
    } else if strings.Contains(proximity, "1.") {
        score += 1
    }

    // Size factor (larger lots better for chains)
    if property.HdpData.HomeInfo.LotAreaValue > 87120 { // > 2 acres
        score += 2
    } else if property.HdpData.HomeInfo.LotAreaValue > 43560 { // > 1 acre
        score += 1
    }

    // Price factor (reasonable for development)
    if property.UnformattedPrice < 500000 && property.UnformattedPrice > 100000 {
        score += 1
    }

    switch {
    case score >= 7:
        return "Very High"
    case score >= 5:
        return "High"
    case score >= 3:
        return "Medium"
    default:
        return "Low"
    }
}

// Calculate price per square foot
func calculatePricePerSqFt(property search.ListResult) float64 {
    if property.HdpData.HomeInfo.LotAreaValue > 0 {
        return float64(property.UnformattedPrice) / float64(property.HdpData.HomeInfo.LotAreaValue)
    }
    return 0.0
}

// Brain API Handlers - RealEstateAPI Integration

// brainAutoCompleteHandler handles address autocomplete requests
func brainAutoCompleteHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request struct {
        Search      string   `json:"search"`
        SearchTypes []string `json:"search_types"`
    }

    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }

    if len(request.Search) < 3 {
        writeJSONResponse(w, map[string]interface{}{
            "statusCode":    400,
            "statusMessage": "Bad Request",
            "message":       "Search query must be at least 3 characters long",
            "results":       []interface{}{},
            "data":          []interface{}{},
        })
        return
    }

    result, err := realEstateClient.AutoComplete(request.Search, request.SearchTypes)
    if err != nil {
        log.Printf("AutoComplete API error: %v", err)
        http.Error(w, fmt.Sprintf("AutoComplete API error: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, result)
}

// brainPropertySearchHandler handles comprehensive property search requests
func brainPropertySearchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request realestate.PropertySearchRequest
    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }

    result, err := realEstateClient.PropertySearch(&request)
    if err != nil {
        log.Printf("PropertySearch API error: %v", err)
        http.Error(w, fmt.Sprintf("PropertySearch API error: %v", err), http.StatusInternalServerError)
        return
    }

    response := map[string]interface{}{
        "success": true,
        "data":    result,
    }

    writeJSONResponse(w, response)
}

// brainPropertyDetailHandler handles property detail requests
func brainPropertyDetailHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request realestate.PropertyDetailRequest
    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }

    result, err := realEstateClient.PropertyDetail(&request)
    if err != nil {
        log.Printf("PropertyDetail API error: %v", err)
        http.Error(w, fmt.Sprintf("PropertyDetail API error: %v", err), http.StatusInternalServerError)
        return
    }

    response := map[string]interface{}{
        "success": true,
        "data":    result,
    }

    writeJSONResponse(w, response)
}

// brainPropertyMappingHandler handles property mapping (pins) requests
func brainPropertyMappingHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request map[string]interface{}
    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }

    // Validate that either IDs or search parameters are provided
    if request["ids"] == nil && request["search"] == nil {
        http.Error(w, "Either property IDs or search parameters are required", http.StatusBadRequest)
        return
    }

    resp, err := realEstateClient.MakeRequest("/v2/PropertyMapping", request)
    if err != nil {
        log.Printf("PropertyMapping API error: %v", err)
        http.Error(w, fmt.Sprintf("PropertyMapping API error: %v", err), http.StatusInternalServerError)
        return
    }

    var result map[string]interface{}
    if err := realEstateClient.ParseResponse(resp, &result); err != nil {
        log.Printf("PropertyMapping response parse error: %v", err)
        http.Error(w, fmt.Sprintf("PropertyMapping response parse error: %v", err), http.StatusInternalServerError)
        return
    }

    response := map[string]interface{}{
        "success": true,
        "data":    result,
    }

    writeJSONResponse(w, response)
}

// brainPropertyCompsHandler handles property comparables requests
func brainPropertyCompsHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request map[string]interface{}
    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }

    // Default to v3 API, but allow v2 if specified
    endpoint := "/v3/PropertyComps"
    if version, ok := request["version"].(string); ok && version == "v2" {
        endpoint = "/v2/PropertyComps"
    }

    resp, err := realEstateClient.MakeRequest(endpoint, request)
    if err != nil {
        log.Printf("PropertyComps API error: %v", err)
        http.Error(w, fmt.Sprintf("PropertyComps API error: %v", err), http.StatusInternalServerError)
        return
    }

    var result map[string]interface{}
    if err := realEstateClient.ParseResponse(resp, &result); err != nil {
        log.Printf("PropertyComps response parse error: %v", err)
        http.Error(w, fmt.Sprintf("PropertyComps response parse error: %v", err), http.StatusInternalServerError)
        return
    }

    response := map[string]interface{}{
        "success": true,
        "data":    result,
    }

    writeJSONResponse(w, response)
}

// brainSkipTraceHandler handles skip trace requests for owner contact information
func brainSkipTraceHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request map[string]interface{}
    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }

    // Check if this is a bulk request
    endpoint := "/v1/SkipTrace"
    if _, isBulk := request["properties"]; isBulk {
        endpoint = "/v1/SkipTraceBulk"
    }

    resp, err := realEstateClient.MakeRequest(endpoint, request)
    if err != nil {
        log.Printf("SkipTrace API error: %v", err)
        http.Error(w, fmt.Sprintf("SkipTrace API error: %v", err), http.StatusInternalServerError)
        return
    }

    var result map[string]interface{}
    if err := realEstateClient.ParseResponse(resp, &result); err != nil {
        log.Printf("SkipTrace response parse error: %v", err)
        http.Error(w, fmt.Sprintf("SkipTrace response parse error: %v", err), http.StatusInternalServerError)
        return
    }

    response := map[string]interface{}{
        "success": true,
        "data":    result,
    }

    writeJSONResponse(w, response)
}

// Search sold properties endpoint - migrated from devtesting
func searchSoldHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Parse query parameters
    neLat, _ := strconv.ParseFloat(r.URL.Query().Get("neLat"), 64)
    neLong, _ := strconv.ParseFloat(r.URL.Query().Get("neLong"), 64)
    swLat, _ := strconv.ParseFloat(r.URL.Query().Get("swLat"), 64)
    swLong, _ := strconv.ParseFloat(r.URL.Query().Get("swLong"), 64)

    pagination, _ := strconv.Atoi(r.URL.Query().Get("pagination"))
    if pagination == 0 {
        pagination = 1
    }

    zoom, _ := strconv.Atoi(r.URL.Query().Get("zoom"))
    if zoom == 0 {
        zoom = 10
    }

    // Use proxy rotator
    var proxyURL *url.URL
    if globalProxyRotator != nil && globalProxyRotator.Count() > 0 {
        proxyURL = globalProxyRotator.GetNext()
    }

    // Call the search function for sold properties
    listResults, mapResults, err := search.Sold(
        pagination, zoom, neLat, neLong, swLat, swLong,
        proxyURL,
    )

    if err != nil {
        log.Printf("Error searching sold properties: %v", err)
        http.Error(w, "Error searching sold properties", http.StatusInternalServerError)
        return
    }

    response := map[string]interface{}{
        "listResults": listResults,
        "mapResults":  mapResults,
        "total":       len(listResults),
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}

// Search rental properties endpoint - migrated from devtesting
func searchRentalsHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Parse query parameters
    neLat, _ := strconv.ParseFloat(r.URL.Query().Get("neLat"), 64)
    neLong, _ := strconv.ParseFloat(r.URL.Query().Get("neLong"), 64)
    swLat, _ := strconv.ParseFloat(r.URL.Query().Get("swLat"), 64)
    swLong, _ := strconv.ParseFloat(r.URL.Query().Get("swLong"), 64)

    pagination, _ := strconv.Atoi(r.URL.Query().Get("pagination"))
    if pagination == 0 {
        pagination = 1
    }

    zoom, _ := strconv.Atoi(r.URL.Query().Get("zoom"))
    if zoom == 0 {
        zoom = 10
    }

    // Use proxy rotator
    var proxyURL *url.URL
    if globalProxyRotator != nil && globalProxyRotator.Count() > 0 {
        proxyURL = globalProxyRotator.GetNext()
    }

    // Call the search function for rental properties
    listResults, mapResults, err := search.ForRent(
        pagination, zoom, neLat, neLong, swLat, swLong,
        proxyURL,
    )

    if err != nil {
        log.Printf("Error searching rental properties: %v", err)
        http.Error(w, "Error searching rental properties", http.StatusInternalServerError)
        return
    }

    response := map[string]interface{}{
        "listResults": listResults,
        "mapResults":  mapResults,
        "total":       len(listResults),
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}

// Additional RealEstateAPI Proxy Handlers

// brainPropertyDetailBulkHandler handles bulk property detail requests
func brainPropertyDetailBulkHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request map[string]interface{}
    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    // Call RealEstateAPI PropertyDetailBulk endpoint
    response, err := realEstateClient.PropertyDetailBulk(request)
    if err != nil {
        log.Printf("Error calling PropertyDetailBulk API: %v", err)
        http.Error(w, fmt.Sprintf("API request failed: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, response)
}

// brainPropertyCompsV3Handler handles property comps v3 requests
func brainPropertyCompsV3Handler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request map[string]interface{}
    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    // Call RealEstateAPI PropertyComps v3 endpoint using generic interface
    resp, err := realEstateClient.MakeRequest("/v3/PropertyComps", request)
    if err != nil {
        log.Printf("Error calling PropertyComps v3 API: %v", err)
        http.Error(w, fmt.Sprintf("API request failed: %v", err), http.StatusInternalServerError)
        return
    }

    var response map[string]interface{}
    if err := realEstateClient.ParseResponse(resp, &response); err != nil {
        log.Printf("Error parsing PropertyComps v3 response: %v", err)
        http.Error(w, fmt.Sprintf("Response parsing failed: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, response)
}

// brainPropertyCompsV2Handler handles property comps v2 requests
func brainPropertyCompsV2Handler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request map[string]interface{}
    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    // Call RealEstateAPI PropertyComps v2 endpoint
    response, err := realEstateClient.PropertyCompsV2(request)
    if err != nil {
        log.Printf("Error calling PropertyComps v2 API: %v", err)
        http.Error(w, fmt.Sprintf("API request failed: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, response)
}

// brainInvoluntaryLiensHandler handles involuntary liens requests
func brainInvoluntaryLiensHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var request map[string]interface{}
    if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
        http.Error(w, "Invalid request body", http.StatusBadRequest)
        return
    }

    // Call RealEstateAPI InvoluntaryLiens endpoint
    response, err := realEstateClient.InvoluntaryLiens(request)
    if err != nil {
        log.Printf("Error calling InvoluntaryLiens API: %v", err)
        http.Error(w, fmt.Sprintf("API request failed: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, response)
}
